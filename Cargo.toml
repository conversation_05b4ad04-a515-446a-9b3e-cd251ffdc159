# ESP32 Manifold Project
# Embedded system for manifold control with networking capabilities

[package]
name = "esp32-manifold"
version = "0.1.0"
edition = "2021"

# Binary configuration
[[bin]]
name = "esp32-manifold"
path = "./src/bin/main.rs"
test = false

# Library configuration
[lib]
test = false

# Test configuration
[[test]]
name = "hello_test"
harness = false

# =============================================================================
# DEPENDENCIES
# =============================================================================

[dependencies]
# Core ESP32 and Hardware Abstraction Layer
esp-hal = { version = "=1.0.0-beta.1", features = [
  "defmt",
  "esp32",
  "unstable",
] }
esp-hal-embassy = { version = "0.8.1", features = ["defmt", "esp32"] }
esp-bootloader-esp-idf = "0.1.0"

# Embassy async runtime and utilities
embassy-executor = { version = "0.7.0", features = [
  "defmt",
  "task-arena-size-20480",
] }
embassy-time = { version = "0.4.0", features = ["defmt"] }

# Networking stack
embassy-net = { version = "0.7.0", features = [
  "defmt",
  "dhcpv4",
  "medium-ethernet",
  "tcp",
  "udp",
] }
esp-wifi = { version = "0.14.1", features = [
  "builtin-scheduler",
  "defmt",
  "esp-alloc",
  "esp32",
  "smoltcp",
  "wifi",
] }
smoltcp = { version = "0.12.0", default-features = false, features = [
  "defmt",
  "medium-ethernet",
  "multicast",
  "proto-dhcpv4",
  "proto-dns",
  "proto-ipv4",
  "socket-dns",
  "socket-icmp",
  "socket-raw",
  "socket-tcp",
  "socket-udp",
] } # For more networking protocol support see https://crates.io/crates/edge-net

picoserve = { version = "0.16.0", features = ["embassy"] }

# Embedded I/O
embedded-io = { version = "0.6.1", features = ["defmt-03"] }
embedded-io-async = { version = "0.6.1", features = ["defmt-03"] }

# Memory management and allocation
esp-alloc = { version = "0.8.0", features = ["defmt"] }
static_cell = { version = "2.1.0", features = ["nightly"] }

# Debugging and logging
defmt = "1.0.1"
panic-rtt-target = { version = "0.2.0", features = ["defmt"] }
rtt-target = { version = "0.6.1", features = ["defmt"] }

# Utilities
critical-section = "1.2.0"
wyhash = "0.6.0"
hashbrown = "0.15.4"

# =============================================================================
# DEVELOPMENT DEPENDENCIES
# =============================================================================

[dev-dependencies]
embedded-test = { version = "0.6.0", features = [
  "defmt",
  "embassy",
  "external-executor",
  "xtensa-semihosting",
] }

# =============================================================================
# BUILD PROFILES
# =============================================================================

[profile.dev]
# Rust debug is too slow for embedded systems
# Always build with some optimization for development
opt-level = "s"

[profile.release]
# Optimized for size and performance on ESP32
codegen-units = 1        # LLVM can perform better optimizations using a single thread
debug = 2
debug-assertions = false
incremental = false
lto = 'fat'
opt-level = 's'
overflow-checks = false
