//! # Service Layer
//!
//! This module provides a high-level service interface for manifold control operations.
//! It wraps the low-level `Manifold` functionality with business logic, validation,
//! error handling, and async-compatible operations suitable for API consumption.
//!
//! ## Design Principles
//!
//! - **Safety First**: All operations default to safe states and include validation
//! - **Async Compatible**: All operations are async-ready for Embassy runtime
//! - **Bounded Operations**: Fixed memory allocation, no dynamic growth
//! - **Error Transparency**: Rich error information for API responses
//! - **Idempotent**: Operations can be safely repeated
//!
//! ## Example
//!
//! ```rust
//! use esp32_manifold::service::{ManifoldService, ServiceError};
//!
//! let mut service = ManifoldService::new(manifold);
//!
//! // Turn on a valve with validation
//! match service.turn_valve_on("Valve 1").await {
//!     Ok(status) => println!("Valve turned on: {:?}", status),
//!     Err(ServiceError::ValveNotFound) => println!("Valve not found"),
//!     Err(e) => println!("Error: {:?}", e),
//! }
//! ```

use defmt::{debug, error, info, warn};
use embassy_time::{Duration, Timer};

use crate::manifold::{Manifold, Valve};

/// Maximum number of valves supported by the system
/// This helps bound memory usage and prevents resource exhaustion
pub const MAX_VALVES: usize = 16;

/// Maximum length for valve names to prevent unbounded string operations
pub const MAX_VALVE_NAME_LEN: usize = 32;

/// Default timeout for valve operations (in milliseconds)
pub const DEFAULT_OPERATION_TIMEOUT_MS: u64 = 5000;

/// Service layer errors that can occur during manifold operations
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ServiceError {
    /// The specified valve was not found in the manifold
    ValveNotFound,
    /// The valve name is invalid (too long, empty, or contains invalid characters)
    InvalidValveName,
    /// The manifold has reached its maximum capacity
    ManifoldFull,
    /// Operation timed out
    OperationTimeout,
    /// The valve is already in the requested state
    ValveAlreadyInState,
    /// System is in emergency halt state and operations are disabled
    SystemHalted,
    /// Internal system error occurred
    InternalError,
}

impl ServiceError {
    /// Get a human-readable description of the error
    pub fn description(&self) -> &'static str {
        match self {
            ServiceError::ValveNotFound => "Valve not found",
            ServiceError::InvalidValveName => "Invalid valve name",
            ServiceError::ManifoldFull => "Manifold at maximum capacity",
            ServiceError::OperationTimeout => "Operation timed out",
            ServiceError::ValveAlreadyInState => "Valve already in requested state",
            ServiceError::SystemHalted => "System in emergency halt state",
            ServiceError::InternalError => "Internal system error",
        }
    }

    /// Get the error code for API responses
    pub fn code(&self) -> u16 {
        match self {
            ServiceError::ValveNotFound => 404,
            ServiceError::InvalidValveName => 400,
            ServiceError::ManifoldFull => 409,
            ServiceError::OperationTimeout => 408,
            ServiceError::ValveAlreadyInState => 409,
            ServiceError::SystemHalted => 503,
            ServiceError::InternalError => 500,
        }
    }
}

/// Status information for a valve
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct ValveStatus {
    /// Whether the valve is currently on
    pub is_on: bool,
    /// The name of the valve
    pub name: &'static str,
}

/// Overall status of the manifold system
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct ManifoldStatus {
    /// Total number of valves in the system
    pub total_valves: usize,
    /// Number of valves currently on
    pub valves_on: usize,
    /// Whether the system is in emergency halt state
    pub is_halted: bool,
}

/// Result type for service operations
pub type ServiceResult<T> = Result<T, ServiceError>;

/// High-level service interface for manifold control operations
///
/// This service wraps the low-level `Manifold` with business logic, validation,
/// and async-compatible operations. It provides a clean interface for API handlers
/// while ensuring safety and proper error handling.
pub struct ManifoldService {
    /// The underlying manifold controller
    manifold: Manifold,
    /// Whether the system is in emergency halt state
    is_halted: bool,
    /// Operation timeout duration
    operation_timeout: Duration,
}

impl ManifoldService {
    /// Create a new ManifoldService wrapping the provided manifold
    pub fn new(manifold: Manifold) -> Self {
        info!("Initializing ManifoldService");
        Self {
            manifold,
            is_halted: false,
            operation_timeout: Duration::from_millis(DEFAULT_OPERATION_TIMEOUT_MS),
        }
    }

    /// Set the operation timeout for valve operations
    pub fn set_operation_timeout(&mut self, timeout_ms: u64) {
        self.operation_timeout = Duration::from_millis(timeout_ms);
        debug!("Operation timeout set to {}ms", timeout_ms);
    }

    /// Validate a valve name according to system constraints
    fn validate_valve_name(name: &str) -> ServiceResult<()> {
        if name.is_empty() {
            return Err(ServiceError::InvalidValveName);
        }

        if name.len() > MAX_VALVE_NAME_LEN {
            return Err(ServiceError::InvalidValveName);
        }

        // Check for valid characters (alphanumeric, spaces, hyphens, underscores)
        if !name
            .chars()
            .all(|c| c.is_alphanumeric() || c == ' ' || c == '-' || c == '_')
        {
            return Err(ServiceError::InvalidValveName);
        }

        Ok(())
    }

    /// Check if the system is in a valid state for operations
    fn check_system_state(&self) -> ServiceResult<()> {
        if self.is_halted {
            return Err(ServiceError::SystemHalted);
        }
        Ok(())
    }

    /// Get the current status of a specific valve
    pub async fn get_valve_status(&self, name: &str) -> ServiceResult<ValveStatus> {
        Self::validate_valve_name(name)?;

        let valve = self
            .manifold
            .get_valve(name)
            .ok_or(ServiceError::ValveNotFound)?;

        Ok(ValveStatus {
            is_on: valve.is_on(),
            name: valve.name(),
        })
    }

    /// Get the overall status of the manifold system
    pub async fn get_manifold_status(&self) -> ServiceResult<ManifoldStatus> {
        let valves = self.manifold.get_valves();
        let total_valves = valves.len();
        let valves_on = valves.values().filter(|v| v.is_on()).count();

        Ok(ManifoldStatus {
            total_valves,
            valves_on,
            is_halted: self.is_halted,
        })
    }

    /// Turn on a specific valve with validation and timeout
    pub async fn turn_valve_on(&mut self, name: &str) -> ServiceResult<ValveStatus> {
        Self::validate_valve_name(name)?;
        self.check_system_state()?;

        // Check current state to provide idempotent behavior
        if let Some(valve) = self.manifold.get_valve(name) {
            if valve.is_on() {
                debug!("Valve {} already on", name);
                return Err(ServiceError::ValveAlreadyInState);
            }
        } else {
            return Err(ServiceError::ValveNotFound);
        }

        // Perform the operation with timeout
        let operation = async {
            if let Some(valve) = self.manifold.get_valve_mut(name) {
                valve.on();
                info!("Valve {} turned ON", name);

                // Small delay to ensure hardware state settles
                Timer::after(Duration::from_millis(10)).await;

                Ok(ValveStatus {
                    is_on: true,
                    name: valve.name(),
                })
            } else {
                Err(ServiceError::ValveNotFound)
            }
        };

        // Apply timeout to the operation
        embassy_time::with_timeout(self.operation_timeout, operation)
            .await
            .map_err(|_| {
                error!("Valve {} turn-on operation timed out", name);
                ServiceError::OperationTimeout
            })?
    }

    /// Turn off a specific valve with validation and timeout
    pub async fn turn_valve_off(&mut self, name: &str) -> ServiceResult<ValveStatus> {
        Self::validate_valve_name(name)?;
        // Note: Allow turning off valves even when halted for safety

        // Check current state to provide idempotent behavior
        if let Some(valve) = self.manifold.get_valve(name) {
            if !valve.is_on() {
                debug!("Valve {} already off", name);
                return Err(ServiceError::ValveAlreadyInState);
            }
        } else {
            return Err(ServiceError::ValveNotFound);
        }

        // Perform the operation with timeout
        let operation = async {
            if let Some(valve) = self.manifold.get_valve_mut(name) {
                valve.off();
                info!("Valve {} turned OFF", name);

                // Small delay to ensure hardware state settles
                Timer::after(Duration::from_millis(10)).await;

                Ok(ValveStatus {
                    is_on: false,
                    name: valve.name(),
                })
            } else {
                Err(ServiceError::ValveNotFound)
            }
        };

        // Apply timeout to the operation
        embassy_time::with_timeout(self.operation_timeout, operation)
            .await
            .map_err(|_| {
                error!("Valve {} turn-off operation timed out", name);
                ServiceError::OperationTimeout
            })?
    }

    /// Emergency halt - turn off all valves immediately
    pub async fn emergency_halt(&mut self) -> ServiceResult<ManifoldStatus> {
        warn!("Emergency halt initiated");
        self.is_halted = true;

        // Turn off all valves immediately
        self.manifold.halt();

        // Small delay to ensure all hardware states settle
        Timer::after(Duration::from_millis(50)).await;

        info!("Emergency halt completed - all valves OFF");

        self.get_manifold_status().await
    }

    /// Clear the emergency halt state (but keep valves off)
    pub async fn clear_halt(&mut self) -> ServiceResult<ManifoldStatus> {
        if !self.is_halted {
            return Err(ServiceError::ValveAlreadyInState);
        }

        self.is_halted = false;
        info!("Emergency halt state cleared");

        self.get_manifold_status().await
    }

    /// Add a new valve to the manifold
    pub fn add_valve(&mut self, valve: Valve) -> ServiceResult<()> {
        let valves = self.manifold.get_valves();
        if valves.len() >= MAX_VALVES {
            return Err(ServiceError::ManifoldFull);
        }

        let valve_name = valve.name();
        Self::validate_valve_name(valve_name)?;

        self.manifold.add_valve(valve);
        info!("Added valve: {}", valve_name);

        Ok(())
    }

    /// Get a list of all valve names in the system
    pub fn get_valve_names(&self) -> impl Iterator<Item = &'static str> + '_ {
        self.manifold.get_valves().keys().copied()
    }

    /// Check if a valve exists in the system
    pub fn valve_exists(&self, name: &str) -> bool {
        self.manifold.get_valve(name).is_some()
    }
}
