//! # Application Module
//!
//! This module contains the main application logic and state management,
//! separated from hardware initialization concerns.

use defmt::info;
use embassy_executor::Spawner;
use embassy_time::{Duration, Timer};

/// Application-specific errors
#[derive(Debug)]
pub enum AppError {
    /// Failed to initialize WiFi controller
    WifiInitError,
    /// Failed to initialize WiFi interface
    WifiInterfaceError,
}

/// Main application state and logic
pub struct App {
    // For now, keep the struct simple - WiFi components will be added later when needed
}

impl App {
    /// Create a new App instance with initialized WiFi components
    pub fn new(
        wifi_init: &esp_wifi::EspWifiController,
        wifi_peripheral: esp_hal::peripherals::WIFI,
    ) -> Result<Self, AppError> {
        // Initialize WiFi but don't store the components yet (matching original behavior)
        let (_wifi_controller, _wifi_interfaces) =
            esp_wifi::wifi::new(wifi_init, wifi_peripheral).map_err(|_| AppError::WifiInitError)?;

        Ok(Self {})
    }

    /// Run the main application loop
    pub async fn run(&mut self, spawner: Spawner) -> ! {
        info!("App started!");

        // TODO: Spawn some tasks
        let _ = spawner;

        loop {
            info!("Hello world!");
            Timer::after(Duration::from_secs(1)).await;
        }
    }
}
