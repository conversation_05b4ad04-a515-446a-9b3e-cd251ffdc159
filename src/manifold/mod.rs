//! # Manifold
//!
//! This module provides a way to control multiple valves with a single
//! interface. This is done by creating a `Manifold` and adding `Valve`s to it.
//! The `Manifold` can then be used to control all of the valves at once.
//!
//! ## Example
//!
//! ```rust
//! use esp32_garden_manifold::manifold::{Manifold, Valve};
//! use esp_hal::gpio::{Level, Output, OutputConfig};
//!
//! let mut manifold = Manifold::default();
//!
//! // Initialize the valves
//! manifold.add_valve(Valve::new(
//!     Output::new(peripherals.GPIO2, Level::Low, OutputConfig::default()),
//!     "Valve 1",
//! ));
//! manifold.add_valve(Valve::new(
//!     Output::new(peripherals.GPIO4, Level::Low, OutputConfig::default()),
//!     "Valve 2",
//! ));
//!
//! // Turn on valve 1
//! match manifold.get_valve_mut("Valve 1") {
//!     Some(valve) => valve.on(),
//!     None => rprintln!("Valve 1 not found"),
//! }
//! ```

use esp_hal::gpio::Output;
use rtt_target::rprintln;

use crate::hash;

/// A solenoid valve controlled by a relay
pub struct Valve {
    pin: Output<'static>,
    state: bool,
    name: &'static str,
}

impl Valve {
    pub fn new(pin: Output<'static>, name: &'static str) -> Self {
        Self {
            pin,
            state: false,
            name,
        }
    }

    pub fn on(&mut self) {
        self.pin.set_high();
        self.state = true;
        rprintln!("{} ON", self.name);
    }

    pub fn off(&mut self) {
        self.pin.set_low();
        self.state = false;
        rprintln!("{} OFF", self.name);
    }

    pub fn is_on(&self) -> bool {
        self.state
    }

    pub fn name(&self) -> &'static str {
        self.name
    }
}

/// A map of valve names to `Valve`s
type ValveMap = hashbrown::HashMap<&'static str, Valve, hash::WyBuildHasher>;

/// The main manifold controller
pub struct Manifold {
    valves: ValveMap,
}

impl Manifold {
    fn new() -> Self {
        Self {
            valves: ValveMap::with_hasher(hash::WyBuildHasher::default()),
        }
    }

    pub fn add_valve(&mut self, valve: Valve) {
        let name = valve.name();
        self.valves.insert(name, valve);
    }

    pub fn get_valve(&self, name: &str) -> Option<&Valve> {
        self.valves.get(name)
    }

    pub fn get_valve_mut(&mut self, name: &str) -> Option<&mut Valve> {
        self.valves.get_mut(name)
    }

    pub fn get_valves(&self) -> &ValveMap {
        &self.valves
    }

    pub fn halt(&mut self) {
        for valve in self.valves.values_mut() {
            valve.off();
        }
    }
}

impl Default for Manifold {
    fn default() -> Self {
        Self::new()
    }
}
